---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: coddn-api-dev
  namespace: development
  labels:
    app: coddn-api-dev
spec:
  replicas: 5
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: coddn-api-dev
  template:
    metadata:
      labels:
        app: coddn-api-dev
    spec:
      containers:
        - name: coddn-api-dev
          image: 399444019738.dkr.ecr.us-east-2.amazonaws.com/development/coddn-api:latest
          ports:
            - containerPort: 3000
          env:
            - name: PORT
              value: '3000'
            - name: RID_TOPIC
              value: RID_DEV
