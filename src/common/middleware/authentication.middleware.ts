import { Injectable, Logger, NestMiddleware } from '@nestjs/common';
import axios from 'axios';
import { Request, Response, NextFunction } from 'express';
import { auth, AuthOptions } from 'express-oauth2-jwt-bearer'; // Import AuthOptions
import * as jwt from 'jsonwebtoken'; // Import jwt for decoding JWT tokens
import { User } from 'src/domains/users/user.schema';
import { UsersService } from 'src/domains/users/users.service';

@Injectable()
export class AuthenticationMiddleware implements NestMiddleware {
  constructor(private readonly userService: UsersService, 
              private readonly logger: Logger
  ) {}
  async use(req: Request, res: Response, next: NextFunction) {
    if (req.baseUrl === '') {
      res.status(200).send('Root path is skipped');
    } else if (req.baseUrl === "/python/heartBeatSimulation" || req.baseUrl === "/python/liveSimulation"){
      next()
      return;
    }
    else {
    this.logger.log(`AuthMiddleware: ${req.baseUrl}, Method: ${req.method}`);
      const jwtCheck = auth({
        audience: process.env.AUTH0_AUDIENCE,
        issuerBaseURL: process.env.AUTH0_ISSUER_URL,
        tokenSigningAlg: 'RS256',
      });

      jwtCheck(req, res, async (err) => {
        if (err) {
          return res.status(401).json({ message: 'Unauthorized', err });
        }

        const token = req.headers.authorization.split(' ')[1];
        const decodedToken = jwt.decode(token);

        //By Default this uses default credentials if the token is generated from postman
        req['sub'] = (decodedToken['sub'] as string).includes('@clients')? process.env.DEFAULT_USER_SUB : decodedToken['sub'] as string;
        req['org_id'] = (decodedToken['sub'] as string).includes('@clients')? process.env.DEAFULT_ORG : decodedToken['org_id'] as string;
        req['permissions'] = decodedToken['permissions'] as string[];
        const promises = [
          this.userService.findOne({
            sub: req['sub'],
          }),
          this.userService.syncUserRoles(req['sub'], req['org_id']),
        ];
        let [user, userRoles] = await Promise.all(promises);
        req['roles'] = (userRoles as any[]).map((role) => (role.name as string).toLocaleLowerCase());

        if (!user || req['org_id'] !== (user as User).org_id) {
          const auth0Info = await axios.get(`${process.env.AUTH0_ISSUER_URL}/userinfo`, {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          });
          (user as User) = JSON.parse(JSON.stringify(await this.userService.syncUserWithAuth0(auth0Info)));
        }

        req['user'] = {
          ...user,
          org_id: req['org_id'],
        };

        
        next();
      });
    }
  }
}